package service

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/utils"
	"gorm.io/gorm"
)

type UserServiceInterface interface {
	CreateUser(ctx context.Context, email, invitationCode, referrerCode string) (*model.User, error)
	CreateUserWallet(ctx context.Context, userID uuid.UUID, chain model.ChainType, walletAddress string, walletID *uuid.UUID, walletAccountID *uuid.UUID) (*model.UserWallet, error)
	UpdateFirstLoginStatus(ctx context.Context, userID uuid.UUID) (*model.User, error)
	UpdateWalletExportStatus(ctx context.Context, userID uuid.UUID) (*model.User, error)
	UpdateUserInvitationCode(ctx context.Context, userID uuid.UUID, chain, name, walletAddress string, walletID, walletAccountID *uuid.UUID,
		walletType, invitationCode, email string, isFirstLogin, isExportedWallet bool) (*model.User, error)
	GetUserByID(ctx context.Context, id uuid.UUID) (*model.User, error)
	GetUserByEmail(ctx context.Context, email string) (*model.User, error)
	GetUserWallets(ctx context.Context, userID uuid.UUID) ([]model.UserWallet, error)
	GetReferralInfo(ctx context.Context, userID uuid.UUID) (*model.Referral, error)
	GetReferralSnapshot(ctx context.Context, userID uuid.UUID) (*model.ReferralSnapshot, error)
	GetUserByInvitationCode(ctx context.Context, invitationCode string) (*model.User, error)
	CreateUserWithReferral(ctx context.Context, referrerID uuid.UUID, userID string) error
}

type UserService struct {
	userRepo repo.UserRepositoryInterface
}

func NewUserService() UserServiceInterface {
	return &UserService{
		userRepo: repo.NewUserRepository(),
	}
}

func (s *UserService) CreateUser(ctx context.Context, email, invitationCode, referrerCode string) (*model.User, error) {
	// Normalize and validate email
	email = utils.NormalizeEmail(email)
	if !utils.IsValidEmail(email) {
		return nil, fmt.Errorf("invalid email format: %s", email)
	}

	// Check if user already exists (only if email is not empty)
	if email != "" {
		existingUser, err := s.userRepo.GetByEmail(ctx, email)
		if err == nil && existingUser != nil {
			return nil, fmt.Errorf("user with email %s already exists", email)
		}
	}

	// Validate invitation code if provided
	if invitationCode != "" {
		if len(invitationCode) < 5 || len(invitationCode) > 15 {
			return nil, fmt.Errorf("invitation code must be 5-15 characters long")
		}
	}

	user := &model.User{
		Email:            utils.StringToPointer(email),
		IsFirstLogin:     true,
		IsExportedWallet: false,
		InvitationCode:   nil, // Will be set if invitationCode is provided
	}

	// Set invitation code if provided
	if invitationCode != "" {
		user.InvitationCode = &invitationCode
	}

	// Create user
	if err := s.userRepo.Create(ctx, user); err != nil {
		return nil, fmt.Errorf("failed to create user: %w", err)
	}

	// Handle referral if referrer code is provided
	if referrerCode != "" {
		if err := s.createReferralRelationship(ctx, user.ID, referrerCode); err != nil {
			// Log error but don't fail user creation
			// In production, you might want to handle this differently
			fmt.Printf("Failed to create referral relationship: %v\n", err)
		}
	}

	return user, nil
}

func (s *UserService) CreateUserWallet(ctx context.Context, userID uuid.UUID, chain model.ChainType, walletAddress string, walletID *uuid.UUID, walletAccountID *uuid.UUID) (*model.UserWallet, error) {
	wallet := &model.UserWallet{
		UserID:          userID,
		Chain:           chain,
		WalletAddress:   walletAddress,
		WalletID:        walletID,
		WalletAccountID: walletAccountID,
	}

	if err := s.userRepo.CreateUserWallet(ctx, wallet); err != nil {
		return nil, fmt.Errorf("failed to create user wallet: %w", err)
	}

	return wallet, nil
}

func (s *UserService) UpdateFirstLoginStatus(ctx context.Context, userID uuid.UUID) (*model.User, error) {
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	user.IsFirstLogin = false
	if err := s.userRepo.Update(ctx, user); err != nil {
		return nil, fmt.Errorf("failed to update user: %w", err)
	}

	return user, nil
}

func (s *UserService) UpdateWalletExportStatus(ctx context.Context, userID uuid.UUID) (*model.User, error) {
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	user.IsExportedWallet = true
	if err := s.userRepo.Update(ctx, user); err != nil {
		return nil, fmt.Errorf("failed to update user: %w", err)
	}

	return user, nil
}

func (s *UserService) UpdateUserInvitationCode(ctx context.Context, userID uuid.UUID, chain, name, walletAddress string,
	walletID, walletAccountID *uuid.UUID, walletType, invitationCode, email string, isFirstLogin, isExportedWallet bool) (*model.User, error) {
	// Validate invitation code format
	if len(invitationCode) < 5 || len(invitationCode) > 15 {
		return nil, fmt.Errorf("invitation code must be 5-15 characters long")
	}

	// Normalize and validate email
	email = utils.NormalizeEmail(email)
	if !utils.IsValidEmail(email) {
		return nil, fmt.Errorf("invalid email format: %s", email)
	}

	newUser := &model.User{
		ID:             userID,
		InvitationCode: &invitationCode,
		Email:          utils.StringToPointer(email), // Set email as pointer, nil if empty
	}

	if err := s.userRepo.Create(ctx, newUser); err != nil {
		return nil, fmt.Errorf("failed to create user: %w", err)
	}
	newUserWallet := &model.UserWallet{
		ID:              uuid.New(),
		UserID:          userID,
		WalletAddress:   walletAddress,
		WalletID:        walletID,
		WalletAccountID: walletAccountID,
	}
	if err := s.userRepo.CreateWallet(ctx, newUserWallet); err != nil {
		return nil, fmt.Errorf("failed to create user wallet: %w", err)
	}

	return newUser, nil
}

func (s *UserService) GetUserByID(ctx context.Context, id uuid.UUID) (*model.User, error) {
	return s.userRepo.GetByID(ctx, id)
}

func (s *UserService) GetUserByEmail(ctx context.Context, email string) (*model.User, error) {
	return s.userRepo.GetByEmail(ctx, email)
}

func (s *UserService) GetUserWallets(ctx context.Context, userID uuid.UUID) ([]model.UserWallet, error) {
	return s.userRepo.GetUserWallets(ctx, userID)
}

func (s *UserService) GetReferralInfo(ctx context.Context, userID uuid.UUID) (*model.Referral, error) {
	return s.userRepo.GetReferralInfo(ctx, userID)
}

func (s *UserService) GetReferralSnapshot(ctx context.Context, userID uuid.UUID) (*model.ReferralSnapshot, error) {
	return s.userRepo.GetReferralSnapshot(ctx, userID)
}

func (s *UserService) createReferralRelationship(ctx context.Context, userID uuid.UUID, referrerCode string) error {
	// Validate referrer code format
	if len(referrerCode) < 5 || len(referrerCode) > 15 {
		return fmt.Errorf("invalid referrer code format: must be 5-15 characters")
	}

	// Find referrer by invitation code
	referrer, err := s.userRepo.GetByInvitationCode(ctx, referrerCode)
	if err != nil {
		return fmt.Errorf("referrer not found with code %s: %w", referrerCode, err)
	}

	// Prevent self-referral
	if referrer.ID == userID {
		return fmt.Errorf("cannot refer yourself")
	}

	// Check if referral relationship already exists
	existingReferral, err := s.userRepo.GetReferralInfo(ctx, userID)
	if err == nil && existingReferral != nil {
		return fmt.Errorf("referral relationship already exists for this user")
	}

	// Create referral relationship
	referral := &model.Referral{
		UserID:     userID,
		ReferrerID: &referrer.ID,
		Depth:      1, // Direct referral
	}

	if err := s.userRepo.CreateReferral(ctx, referral); err != nil {
		return fmt.Errorf("failed to create referral: %w", err)
	}

	return nil
}

func (s *UserService) createReferralRelationshipByID(ctx context.Context, userID uuid.UUID, referrerID string) error {
	referrerUUID, err := uuid.Parse(referrerID)
	if err != nil {
		return fmt.Errorf("invalid referrer ID: %w", err)
	}
	if referrerUUID == userID {
		return fmt.Errorf("cannot refer yourself")
	}
	// Check if the new user has been referred by anyone
	hasReferrer, err := s.userRepo.HasDirectReferral(ctx, userID)
	if err != nil {
		return fmt.Errorf("failed to check existing referrals for user %s: %w", userID, err)
	}
	if hasReferrer {
		return fmt.Errorf("user %s has already been referred by someone else", userID)
	}

	// Checks whether circular referrals are formed (B cannot recommend A if A has already recommended B)
	isCircular, err := s.userRepo.IsInUpline(ctx, referrerUUID, userID)
	if err != nil {
		return fmt.Errorf("failed to check for circular referral: %w", err)
	}
	if isCircular {
		return fmt.Errorf("circular referral detected: %s is already in the upline of %s", userID, referrerUUID)
	}

	return global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		if err := tx.Create(&model.Referral{
			ReferrerID: &referrerUUID,
			UserID:     userID,
			Depth:      1,
		}).Error; err != nil {
			return fmt.Errorf("failed to create direct referral: %w", err)
		}

		referrerRelations, err := s.userRepo.GetAllReferrals(ctx, tx, referrerUUID)
		if err != nil {
			return fmt.Errorf("failed to get all referrals: %w", err)
		}

		for _, rel := range referrerRelations {
			if rel.ReferrerID.String() == userID.String() {
				continue
			}
			isCircular, err := s.userRepo.IsInUpline(ctx, referrerUUID, userID)
			if err != nil {
				continue
			}
			if isCircular {
				continue
			}
			if err := tx.Create(&model.Referral{
				ReferrerID: rel.ReferrerID,
				UserID:     userID,
				Depth:      rel.Depth + 1,
			}).Error; err != nil {
				return fmt.Errorf("failed to create indirect referral (depth %d): %w", rel.Depth+1, err)
			}
		}

		return nil
	})
}

func (s *UserService) GetUserByInvitationCode(ctx context.Context, invitationCode string) (*model.User, error) {
	return s.userRepo.GetByInvitationCode(ctx, invitationCode)
}

func (s *UserService) CreateUserWithReferral(ctx context.Context, referrerID uuid.UUID, userID string) error {
	idParse, err := uuid.Parse(userID)
	if err != nil {
		return fmt.Errorf("invalid user ID: %w", err)
	}

	referral, err := s.userRepo.GetDirectReferral(ctx, idParse, referrerID)

	// If err is nil, the query is successful and the record is found.
	if err == nil && referral != nil {
		return fmt.Errorf("the direct referral relationship between user %s and referrer %s already exists", userID, referrerID.String())
	}

	// If err is not gorm.ErrRecordNotFound, it means that there is another database error and an error should also be reported.
	if err != nil && err != gorm.ErrRecordNotFound {
		return fmt.Errorf("database error when checking referral relationship: %w", err)
	}

	if err := s.createReferralRelationshipByID(ctx, idParse, referrerID.String()); err != nil {
		return err
	}
	return nil
}
